/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: #ffffff;
    color: #000000;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Navigation Bar */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background-color: #ffffff;
    border-bottom: 1px solid #000000;
    z-index: 1000;
    padding: 0 20px;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    font-weight: 700;
    font-size: 1.8rem;
    color: #000000;
}

.login-btn {
    background-color: #000000;
    color: #ffffff;
    padding: 10px 25px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid #000000;
}

.login-btn:hover {
    background-color: #ffffff;
    color: #000000;
    transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 100px 20px 50px;
    background-color: #ffffff;
}

.hero-container {
    max-width: 1200px;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-content {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease forwards;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 10px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.8rem;
    font-weight: 400;
    color: #000000;
    margin-bottom: 15px;
}

.hero-description {
    font-size: 1.1rem;
    color: #333333;
    margin-bottom: 30px;
}

/* Typing Animation */
.typing-demo {
    font-size: 1rem;
    color: #666666;
    margin-bottom: 40px;
    min-height: 25px;
}

.typing-text {
    border-right: 2px solid transparent;
}

.cursor {
    animation: blink 1s infinite;
    color: #000000;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Chatbot Container */
.chatbot-container {
    background-color: #ffffff;
    border: 2px solid #000000;
    border-radius: 15px;
    max-width: 500px;
    width: 100%;
    height: 600px;
    display: flex;
    flex-direction: column;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease 0.3s forwards;
}

.chatbot-header {
    background-color: #000000;
    color: #ffffff;
    padding: 20px;
    border-radius: 13px 13px 0 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.bot-avatar {
    font-size: 1.5rem;
}

.bot-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.status {
    font-size: 0.8rem;
    opacity: 0.8;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #ffffff;
}

.message {
    margin-bottom: 15px;
}

.bot-message .message-content {
    background-color: #f8f8f8;
    border: 1px solid #e0e0e0;
    padding: 15px;
    border-radius: 15px 15px 15px 5px;
    max-width: 85%;
}

.bot-message ul {
    margin-top: 10px;
    padding-left: 20px;
}

.bot-message li {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.chat-input-container {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background-color: #ffffff;
    border-radius: 0 0 13px 13px;
}

.input-wrapper {
    display: flex;
    gap: 10px;
    align-items: center;
}

.chat-input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.chat-input:focus {
    border-color: #000000;
}

.send-btn {
    background-color: #000000;
    color: #ffffff;
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.send-btn:hover {
    background-color: #333333;
    transform: scale(1.05);
}

/* Footer */
.footer {
    background-color: #000000;
    color: #ffffff;
    text-align: center;
    padding: 20px;
    margin-top: 50px;
}

.footer-content p {
    font-size: 0.9rem;
}

/* Animations */
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }
    
    .nav-logo h2 {
        font-size: 1.5rem;
    }
    
    .login-btn {
        padding: 8px 20px;
        font-size: 0.9rem;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.4rem;
    }
    
    .chatbot-container {
        max-width: 100%;
        height: 500px;
    }
    
    .hero-section {
        padding: 90px 15px 30px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .chatbot-container {
        height: 450px;
    }
    
    .chat-messages {
        padding: 15px;
    }
    
    .chatbot-header {
        padding: 15px;
    }
    
    .chat-input-container {
        padding: 15px;
    }
}
