// DOM Elements
const chatInput = document.getElementById('chatInput');
const sendBtn = document.getElementById('sendBtn');
const chatMessages = document.getElementById('chatMessages');
const typingIndicator = document.getElementById('typingIndicator');
const themeToggle = document.getElementById('themeToggle');

// Bot responses
const botResponses = [
    "I'm currently in development phase. Soon I'll be able to help you with all your college queries!",
    "Thank you for your question! I'm being trained to provide accurate information about college services.",
    "Great question! Once fully deployed, I'll have access to all college databases to assist you better.",
    "I appreciate your interest! My full functionality will be available soon with real-time data access.",
    "I'm learning about Bhagwant Institute of Technology to serve you better. Stay tuned for updates!",
    "Your query is noted! I'll be able to provide detailed information about fees, timetables, and more very soon."
];

// Add message to chat
function addMessage(content, isUser = false, animate = true) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;

    const messageBubble = document.createElement('div');
    messageBubble.className = 'message-bubble';

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    if (typeof content === 'string') {
        const p = document.createElement('p');
        p.textContent = content;
        messageContent.appendChild(p);
    } else {
        messageContent.appendChild(content);
    }

    messageBubble.appendChild(messageContent);
    messageDiv.appendChild(messageBubble);

    if (animate) {
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(20px)';
    }

    chatMessages.appendChild(messageDiv);

    if (animate) {
        setTimeout(() => {
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
            messageDiv.style.transition = 'all 0.3s ease-out';
        }, 50);
    }

    // Scroll to bottom
    setTimeout(() => {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }, animate ? 100 : 0);
}

// Show typing indicator
function showTypingIndicator() {
    typingIndicator.classList.add('show');
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Hide typing indicator
function hideTypingIndicator() {
    typingIndicator.classList.remove('show');
}

// Handle sending message
function handleSendMessage() {
    const message = chatInput.value.trim();
    if (message === '') return;

    // Add user message
    addMessage(message, true);

    // Clear input
    chatInput.value = '';

    // Show typing indicator
    showTypingIndicator();

    // Simulate bot response with delay
    const responseDelay = Math.random() * 1000 + 1500; // 1.5-2.5 seconds

    setTimeout(() => {
        hideTypingIndicator();
        const randomResponse = botResponses[Math.floor(Math.random() * botResponses.length)];
        addMessage(randomResponse);
    }, responseDelay);
}

// Handle suggestion chip clicks
function handleChipClick(query) {
    chatInput.value = query;
    handleSendMessage();
}

// Event listeners
sendBtn.addEventListener('click', handleSendMessage);

chatInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        e.preventDefault();
        handleSendMessage();
    }
});

// Theme toggle (non-functional for now)
themeToggle.addEventListener('click', () => {
    // Placeholder for theme toggle functionality
    themeToggle.style.transform = 'rotate(180deg)';
    setTimeout(() => {
        themeToggle.style.transform = 'rotate(0deg)';
    }, 300);
});

// Handle suggestion chips
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('chip')) {
        const query = e.target.getAttribute('data-query');
        handleChipClick(query);
    }
});

// Input focus effects
chatInput.addEventListener('focus', function() {
    this.parentElement.style.transform = 'scale(1.02)';
});

chatInput.addEventListener('blur', function() {
    this.parentElement.style.transform = 'scale(1)';
});

// Send button animation
sendBtn.addEventListener('mousedown', function() {
    this.style.transform = 'scale(0.95)';
});

sendBtn.addEventListener('mouseup', function() {
    this.style.transform = 'scale(1.1)';
    setTimeout(() => {
        this.style.transform = 'scale(1)';
    }, 150);
});

// Auto-resize input on mobile
function adjustInputHeight() {
    if (window.innerWidth <= 768) {
        chatInput.style.fontSize = '16px'; // Prevents zoom on iOS
    }
}

// Initialize
window.addEventListener('load', () => {
    adjustInputHeight();

    // Focus on input for better UX
    setTimeout(() => {
        if (window.innerWidth > 768) {
            chatInput.focus();
        }
    }, 500);
});

window.addEventListener('resize', adjustInputHeight);

// Prevent zoom on iOS when focusing input
if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
    chatInput.addEventListener('focus', function() {
        this.style.fontSize = '16px';
    });
}
