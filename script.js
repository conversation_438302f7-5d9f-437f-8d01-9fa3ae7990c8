// Typing Animation
const typingText = document.getElementById('typingText');
const queries = [
    "Show me BCA 3rd year timetable",
    "How much fee is pending?",
    "Who is the HOD of MCA?",
    "What are the library timings?",
    "Show me exam schedule"
];

let currentQueryIndex = 0;
let currentCharIndex = 0;
let isDeleting = false;
let typingSpeed = 100;

function typeWriter() {
    const currentQuery = queries[currentQueryIndex];
    
    if (isDeleting) {
        typingText.textContent = currentQuery.substring(0, currentCharIndex - 1);
        currentCharIndex--;
        typingSpeed = 50;
    } else {
        typingText.textContent = currentQuery.substring(0, currentCharIndex + 1);
        currentCharIndex++;
        typingSpeed = 100;
    }
    
    if (!isDeleting && currentCharIndex === currentQuery.length) {
        // Pause at end of sentence
        setTimeout(() => {
            isDeleting = true;
        }, 2000);
    } else if (isDeleting && currentCharIndex === 0) {
        isDeleting = false;
        currentQueryIndex = (currentQueryIndex + 1) % queries.length;
    }
    
    setTimeout(typeWriter, typingSpeed);
}

// Start typing animation after page load
window.addEventListener('load', () => {
    setTimeout(typeWriter, 1000);
});

// Chat functionality
const chatInput = document.getElementById('chatInput');
const sendBtn = document.getElementById('sendBtn');
const chatMessages = document.getElementById('chatMessages');

function addMessage(content, isUser = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = content;
    
    messageDiv.appendChild(messageContent);
    chatMessages.appendChild(messageDiv);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function handleSendMessage() {
    const message = chatInput.value.trim();
    if (message === '') return;
    
    // Add user message
    addMessage(message, true);
    
    // Clear input
    chatInput.value = '';
    
    // Simulate bot response
    setTimeout(() => {
        const responses = [
            "I'm currently in development phase. Soon I'll be able to help you with all your college queries!",
            "Thank you for your question! I'm being trained to provide accurate information about college services.",
            "Great question! Once fully deployed, I'll have access to all college databases to assist you better.",
            "I appreciate your interest! My full functionality will be available soon with real-time data access."
        ];
        
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        addMessage(randomResponse);
    }, 1000);
}

// Event listeners
sendBtn.addEventListener('click', handleSendMessage);

chatInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        handleSendMessage();
    }
});

// Add user message styles dynamically
const style = document.createElement('style');
style.textContent = `
    .user-message {
        display: flex;
        justify-content: flex-end;
    }
    
    .user-message .message-content {
        background-color: #000000;
        color: #ffffff;
        padding: 12px 15px;
        border-radius: 15px 15px 5px 15px;
        max-width: 85%;
        margin-left: auto;
    }
`;
document.head.appendChild(style);

// Smooth scrolling for navigation (if needed in future)
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading animation for send button
sendBtn.addEventListener('click', function() {
    this.style.transform = 'scale(0.95)';
    setTimeout(() => {
        this.style.transform = 'scale(1)';
    }, 150);
});

// Mobile menu toggle (for future enhancement)
function toggleMobileMenu() {
    // This can be implemented when mobile menu is needed
    console.log('Mobile menu toggle');
}

// Add some interactive feedback
chatInput.addEventListener('focus', function() {
    this.parentElement.style.transform = 'scale(1.02)';
    this.parentElement.style.transition = 'transform 0.2s ease';
});

chatInput.addEventListener('blur', function() {
    this.parentElement.style.transform = 'scale(1)';
});

// Add entrance animations on scroll (for future sections)
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation (can be expanded)
document.querySelectorAll('.hero-content, .chatbot-container').forEach(el => {
    observer.observe(el);
});
